# 🌤️ Weather App

A beautiful, responsive weather application built with React, Vite, and Tailwind CSS. Get current weather conditions, 5-day forecasts, and manage your favorite locations with a stunning animated UI.

## ✨ Features

### Core Features
- ✅ **Search current weather by city name** - Find weather for any city worldwide
- ✅ **View 5-day forecasts** - Plan ahead with detailed weather predictions
- ✅ **Save favorite locations** - Quick access to your most-checked locations
- ✅ **Detailed weather info** - Temperature, humidity, wind, condition icons, sunrise/sunset
- ✅ **OpenWeatherMap API integration** - Reliable weather data source
- ✅ **Beautiful animated UI** - Smooth transitions and engaging animations
- ✅ **Loading and error states** - Graceful handling of all app states
- ✅ **Mobile-friendly design** - Responsive layout for all devices
- ✅ **Accessibility features** - Screen reader friendly and keyboard navigation

### Bonus Features
- 🌙 **Dark/light theme toggle** - Switch between themes with smooth animations
- 📍 **Current location support** - Use geolocation for instant local weather
- 🔄 **Auto-refresh** - Weather data updates every 30 seconds automatically
- 💾 **Local storage** - Favorites and preferences persist between sessions
- 🎨 **Glass morphism design** - Modern UI with backdrop blur effects
- ⚡ **Fast performance** - Optimized with React hooks and efficient state management

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd weather-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up OpenWeatherMap API (Optional)**
   - Sign up at [OpenWeatherMap](https://openweathermap.org/api)
   - Get your free API key
   - Open `src/utils/weatherApi.js`
   - Replace `'your_api_key_here'` with your actual API key
   - Set `DEMO_MODE = false` to use real API data

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   - Navigate to `http://localhost:5173`
   - Start exploring the weather app!

## 🛠️ Built With

- **React 19** - Modern React with latest features
- **Vite** - Fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework
- **Heroicons** - Beautiful SVG icons
- **date-fns** - Modern JavaScript date utility library
- **OpenWeatherMap API** - Weather data provider

## 📱 Usage

### Search for Weather
1. Use the search bar in the header
2. Type a city name and press Enter
3. Or click the location icon to use your current location

### Manage Favorites
1. Click the heart icon on any weather card to add to favorites
2. Access favorites from the sidebar or dedicated tab
3. Click on any favorite for quick weather access

### Switch Themes
1. Click the sun/moon icon in the header
2. Enjoy smooth transitions between light and dark modes
3. Your preference is automatically saved

### View Forecasts
1. Click the "Forecast" tab after searching for a location
2. View 5-day weather predictions
3. See detailed information for each day

## 🎨 Design Features

- **Glass Morphism** - Modern translucent design elements
- **Smooth Animations** - Engaging micro-interactions
- **Responsive Layout** - Works perfectly on all screen sizes
- **Dark Mode** - Eye-friendly dark theme option
- **Weather-based Backgrounds** - Dynamic backgrounds based on conditions
- **Accessibility** - WCAG compliant design

## 🔧 Configuration

### API Configuration
The app works in demo mode by default with mock data. To use real weather data:

1. Get an API key from OpenWeatherMap
2. Update `src/utils/weatherApi.js`:
   ```javascript
   const API_KEY = 'your_actual_api_key';
   const DEMO_MODE = false;
   ```

### Customization
- **Colors**: Modify `tailwind.config.js` for custom color schemes
- **Animations**: Adjust animation durations in the config
- **Layout**: Customize component layouts in respective files

## 📦 Build for Production

```bash
npm run build
```

The built files will be in the `dist` directory, ready for deployment.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- OpenWeatherMap for providing weather data
- Heroicons for beautiful icons
- Tailwind CSS for the amazing utility framework
- The React team for the fantastic framework

---

**Enjoy exploring the weather! 🌈**
