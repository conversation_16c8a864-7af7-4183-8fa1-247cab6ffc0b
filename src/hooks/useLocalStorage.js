import { useState, useEffect } from 'react';

/**
 * Custom hook for managing localStorage with React state
 * @param {string} key - localStorage key
 * @param {*} initialValue - Initial value if key doesn't exist
 * @returns {[*, Function]} Current value and setter function
 */
export const useLocalStorage = (key, initialValue) => {
  // Get value from localStorage or use initial value
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Update localStorage when state changes
  const setValue = (value) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
};

/**
 * Custom hook for managing favorite locations
 * @returns {Object} Favorites management functions
 */
export const useFavorites = () => {
  const [favorites, setFavorites] = useLocalStorage('weather-favorites', []);

  const addFavorite = (location) => {
    setFavorites(prev => {
      // Check if location already exists
      const exists = prev.some(fav => 
        fav.name.toLowerCase() === location.name.toLowerCase()
      );
      
      if (exists) {
        return prev;
      }
      
      // Add new favorite with timestamp
      const newFavorite = {
        ...location,
        id: Date.now(),
        addedAt: Date.now()
      };
      
      return [...prev, newFavorite];
    });
  };

  const removeFavorite = (locationName) => {
    setFavorites(prev => 
      prev.filter(fav => 
        fav.name.toLowerCase() !== locationName.toLowerCase()
      )
    );
  };

  const isFavorite = (locationName) => {
    return favorites.some(fav => 
      fav.name.toLowerCase() === locationName.toLowerCase()
    );
  };

  const toggleFavorite = (location) => {
    if (isFavorite(location.name)) {
      removeFavorite(location.name);
    } else {
      addFavorite(location);
    }
  };

  const clearFavorites = () => {
    setFavorites([]);
  };

  return {
    favorites,
    addFavorite,
    removeFavorite,
    isFavorite,
    toggleFavorite,
    clearFavorites
  };
};

/**
 * Custom hook for managing theme preference
 * @returns {Object} Theme management functions
 */
export const useTheme = () => {
  const [theme, setTheme] = useLocalStorage('weather-theme', 'light');

  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  };

  const setLightTheme = () => setTheme('light');
  const setDarkTheme = () => setTheme('dark');

  // Apply theme to document
  useEffect(() => {
    const root = window.document.documentElement;
    
    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, [theme]);

  return {
    theme,
    setTheme,
    toggleTheme,
    setLightTheme,
    setDarkTheme,
    isDark: theme === 'dark'
  };
};

/**
 * Custom hook for managing app settings
 * @returns {Object} Settings management functions
 */
export const useSettings = () => {
  const [settings, setSettings] = useLocalStorage('weather-settings', {
    temperatureUnit: 'celsius',
    windSpeedUnit: 'kmh',
    autoRefresh: true,
    refreshInterval: 30000, // 30 seconds
    showNotifications: true,
    use24HourFormat: false
  });

  const updateSetting = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const resetSettings = () => {
    setSettings({
      temperatureUnit: 'celsius',
      windSpeedUnit: 'kmh',
      autoRefresh: true,
      refreshInterval: 30000,
      showNotifications: true,
      use24HourFormat: false
    });
  };

  return {
    settings,
    updateSetting,
    resetSettings
  };
};
