import { useState, useEffect, useCallback } from 'react';
import { 
  getCurrentWeather, 
  getWeatherByCoords, 
  getForecast, 
  getForecastByCoords 
} from '../utils/weatherApi';

/**
 * Custom hook for managing weather data
 * @param {string} initialCity - Initial city to fetch weather for
 * @returns {Object} Weather data and functions
 */
export const useWeatherData = (initialCity = '') => {
  const [currentWeather, setCurrentWeather] = useState(null);
  const [forecast, setForecast] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [currentCity, setCurrentCity] = useState(initialCity);

  /**
   * Fetch weather data for a city
   * @param {string} city - City name
   */
  const fetchWeatherByCity = useCallback(async (city) => {
    if (!city.trim()) {
      setError(new Error('Please enter a city name'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const [weatherData, forecastData] = await Promise.all([
        getCurrentWeather(city),
        getForecast(city)
      ]);

      setCurrentWeather(weatherData);
      setForecast(forecastData);
      setCurrentCity(city);
      setLastUpdated(Date.now());
    } catch (err) {
      setError(err);
      console.error('Error fetching weather data:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Fetch weather data by coordinates
   * @param {number} latitude - Latitude
   * @param {number} longitude - Longitude
   */
  const fetchWeatherByCoords = useCallback(async (latitude, longitude) => {
    setLoading(true);
    setError(null);

    try {
      const [weatherData, forecastData] = await Promise.all([
        getWeatherByCoords(latitude, longitude),
        getForecastByCoords(latitude, longitude)
      ]);

      setCurrentWeather(weatherData);
      setForecast(forecastData);
      setCurrentCity(weatherData.name || 'Current Location');
      setLastUpdated(Date.now());
    } catch (err) {
      setError(err);
      console.error('Error fetching weather data by coordinates:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Refresh current weather data
   */
  const refreshWeather = useCallback(async () => {
    if (currentCity) {
      await fetchWeatherByCity(currentCity);
    }
  }, [currentCity, fetchWeatherByCity]);

  /**
   * Clear all weather data
   */
  const clearWeatherData = () => {
    setCurrentWeather(null);
    setForecast(null);
    setError(null);
    setLastUpdated(null);
    setCurrentCity('');
  };

  // Auto-refresh weather data every 30 seconds if there's current data
  useEffect(() => {
    if (!currentWeather || !currentCity) return;

    const interval = setInterval(() => {
      refreshWeather();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [currentWeather, currentCity, refreshWeather]);

  // Fetch initial weather data if initialCity is provided
  useEffect(() => {
    if (initialCity) {
      fetchWeatherByCity(initialCity);
    }
  }, [initialCity, fetchWeatherByCity]);

  return {
    currentWeather,
    forecast,
    loading,
    error,
    lastUpdated,
    currentCity,
    fetchWeatherByCity,
    fetchWeatherByCoords,
    refreshWeather,
    clearWeatherData
  };
};

/**
 * Custom hook for managing multiple weather locations
 * @returns {Object} Multiple locations weather data and functions
 */
export const useMultipleWeatherData = () => {
  const [locations, setLocations] = useState(new Map());
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState(new Map());

  /**
   * Add a location and fetch its weather data
   * @param {string} city - City name
   */
  const addLocation = useCallback(async (city) => {
    if (!city.trim()) return;

    setLoading(true);
    
    try {
      const weatherData = await getCurrentWeather(city);
      
      setLocations(prev => new Map(prev.set(city, {
        weather: weatherData,
        lastUpdated: Date.now()
      })));
      
      // Clear any previous error for this city
      setErrors(prev => {
        const newErrors = new Map(prev);
        newErrors.delete(city);
        return newErrors;
      });
    } catch (err) {
      setErrors(prev => new Map(prev.set(city, err)));
      console.error(`Error fetching weather for ${city}:`, err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Remove a location
   * @param {string} city - City name
   */
  const removeLocation = useCallback((city) => {
    setLocations(prev => {
      const newLocations = new Map(prev);
      newLocations.delete(city);
      return newLocations;
    });
    
    setErrors(prev => {
      const newErrors = new Map(prev);
      newErrors.delete(city);
      return newErrors;
    });
  }, []);

  /**
   * Refresh weather data for a specific location
   * @param {string} city - City name
   */
  const refreshLocation = useCallback(async (city) => {
    await addLocation(city);
  }, [addLocation]);

  /**
   * Refresh all locations
   */
  const refreshAllLocations = useCallback(async () => {
    const cities = Array.from(locations.keys());
    await Promise.all(cities.map(city => refreshLocation(city)));
  }, [locations, refreshLocation]);

  /**
   * Clear all locations
   */
  const clearAllLocations = () => {
    setLocations(new Map());
    setErrors(new Map());
  };

  return {
    locations: Array.from(locations.entries()).map(([city, data]) => ({
      city,
      ...data
    })),
    loading,
    errors: Array.from(errors.entries()).map(([city, error]) => ({
      city,
      error
    })),
    addLocation,
    removeLocation,
    refreshLocation,
    refreshAllLocations,
    clearAllLocations
  };
};
