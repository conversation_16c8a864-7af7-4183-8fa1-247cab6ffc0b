// OpenWeatherMap API configuration
const API_KEY = 'your_api_key_here'; // Replace with your actual API key
const BASE_URL = 'https://api.openweathermap.org/data/2.5';
const GEO_URL = 'https://api.openweathermap.org/geo/1.0';

// For demo purposes, we'll use a mock API key and simulate responses
const DEMO_MODE = true;

// Mock weather data for demo
const mockWeatherData = {
  current: {
    name: 'New York',
    sys: { country: 'US', sunrise: 1640000000, sunset: 1640040000 },
    main: {
      temp: 22,
      feels_like: 20,
      humidity: 65,
      pressure: 1013
    },
    weather: [{ main: 'Clear', description: 'clear sky', icon: '01d' }],
    wind: { speed: 3.5, deg: 180 },
    visibility: 10000,
    dt: Date.now() / 1000
  },
  forecast: {
    list: [
      {
        dt: Date.now() / 1000 + 86400,
        main: { temp: 25, humidity: 60 },
        weather: [{ main: 'Sunny', description: 'sunny', icon: '01d' }],
        wind: { speed: 2.5 }
      },
      {
        dt: Date.now() / 1000 + 172800,
        main: { temp: 18, humidity: 75 },
        weather: [{ main: 'Clouds', description: 'cloudy', icon: '03d' }],
        wind: { speed: 4.0 }
      },
      {
        dt: Date.now() / 1000 + 259200,
        main: { temp: 15, humidity: 80 },
        weather: [{ main: 'Rain', description: 'light rain', icon: '10d' }],
        wind: { speed: 5.5 }
      },
      {
        dt: Date.now() / 1000 + 345600,
        main: { temp: 20, humidity: 70 },
        weather: [{ main: 'Clear', description: 'clear sky', icon: '01d' }],
        wind: { speed: 3.0 }
      },
      {
        dt: Date.now() / 1000 + 432000,
        main: { temp: 23, humidity: 65 },
        weather: [{ main: 'Sunny', description: 'sunny', icon: '01d' }],
        wind: { speed: 2.8 }
      }
    ]
  }
};

/**
 * Fetch current weather data for a city
 * @param {string} city - City name
 * @returns {Promise<Object>} Weather data
 */
export const getCurrentWeather = async (city) => {
  if (DEMO_MODE) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      ...mockWeatherData.current,
      name: city,
      main: {
        ...mockWeatherData.current.main,
        temp: Math.round(Math.random() * 30 + 5) // Random temp between 5-35°C
      }
    };
  }

  try {
    const response = await fetch(
      `${BASE_URL}/weather?q=${encodeURIComponent(city)}&appid=${API_KEY}&units=metric`
    );
    
    if (!response.ok) {
      throw new Error(`Weather data not found for ${city}`);
    }
    
    return await response.json();
  } catch (error) {
    throw new Error(`Failed to fetch weather data: ${error.message}`);
  }
};

/**
 * Fetch weather data by coordinates
 * @param {number} lat - Latitude
 * @param {number} lon - Longitude
 * @returns {Promise<Object>} Weather data
 */
export const getWeatherByCoords = async (lat, lon) => {
  if (DEMO_MODE) {
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      ...mockWeatherData.current,
      name: 'Current Location',
      coord: { lat, lon }
    };
  }

  try {
    const response = await fetch(
      `${BASE_URL}/weather?lat=${lat}&lon=${lon}&appid=${API_KEY}&units=metric`
    );
    
    if (!response.ok) {
      throw new Error('Weather data not found for current location');
    }
    
    return await response.json();
  } catch (error) {
    throw new Error(`Failed to fetch weather data: ${error.message}`);
  }
};

/**
 * Fetch 5-day weather forecast
 * @param {string} city - City name
 * @returns {Promise<Object>} Forecast data
 */
export const getForecast = async (city) => {
  if (DEMO_MODE) {
    await new Promise(resolve => setTimeout(resolve, 700));
    return mockWeatherData.forecast;
  }

  try {
    const response = await fetch(
      `${BASE_URL}/forecast?q=${encodeURIComponent(city)}&appid=${API_KEY}&units=metric`
    );
    
    if (!response.ok) {
      throw new Error(`Forecast data not found for ${city}`);
    }
    
    return await response.json();
  } catch (error) {
    throw new Error(`Failed to fetch forecast data: ${error.message}`);
  }
};

/**
 * Fetch forecast by coordinates
 * @param {number} lat - Latitude
 * @param {number} lon - Longitude
 * @returns {Promise<Object>} Forecast data
 */
export const getForecastByCoords = async (lat, lon) => {
  if (DEMO_MODE) {
    await new Promise(resolve => setTimeout(resolve, 700));
    return mockWeatherData.forecast;
  }

  try {
    const response = await fetch(
      `${BASE_URL}/forecast?lat=${lat}&lon=${lon}&appid=${API_KEY}&units=metric`
    );
    
    if (!response.ok) {
      throw new Error('Forecast data not found for current location');
    }
    
    return await response.json();
  } catch (error) {
    throw new Error(`Failed to fetch forecast data: ${error.message}`);
  }
};

/**
 * Search for cities by name
 * @param {string} query - Search query
 * @returns {Promise<Array>} Array of city suggestions
 */
export const searchCities = async (query) => {
  if (DEMO_MODE) {
    await new Promise(resolve => setTimeout(resolve, 300));
    const cities = [
      'New York, US', 'London, GB', 'Paris, FR', 'Tokyo, JP', 'Sydney, AU',
      'Berlin, DE', 'Rome, IT', 'Madrid, ES', 'Amsterdam, NL', 'Vienna, AT'
    ];
    return cities.filter(city => 
      city.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 5);
  }

  try {
    const response = await fetch(
      `${GEO_URL}/direct?q=${encodeURIComponent(query)}&limit=5&appid=${API_KEY}`
    );
    
    if (!response.ok) {
      throw new Error('Failed to search cities');
    }
    
    const data = await response.json();
    return data.map(city => `${city.name}, ${city.country}`);
  } catch (error) {
    console.error('City search error:', error);
    return [];
  }
};

/**
 * Get weather icon URL
 * @param {string} iconCode - Weather icon code
 * @returns {string} Icon URL
 */
export const getWeatherIconUrl = (iconCode) => {
  return `https://openweathermap.org/img/wn/${iconCode}@2x.png`;
};
