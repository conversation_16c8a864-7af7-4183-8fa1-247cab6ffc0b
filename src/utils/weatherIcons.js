// Weather condition to emoji/icon mapping
export const weatherIcons = {
  // Clear sky
  '01d': '☀️', // clear sky day
  '01n': '🌙', // clear sky night
  
  // Few clouds
  '02d': '🌤️', // few clouds day
  '02n': '☁️', // few clouds night
  
  // Scattered clouds
  '03d': '☁️', // scattered clouds day
  '03n': '☁️', // scattered clouds night
  
  // Broken clouds
  '04d': '☁️', // broken clouds day
  '04n': '☁️', // broken clouds night
  
  // Shower rain
  '09d': '🌦️', // shower rain day
  '09n': '🌧️', // shower rain night
  
  // Rain
  '10d': '🌦️', // rain day
  '10n': '🌧️', // rain night
  
  // Thunderstorm
  '11d': '⛈️', // thunderstorm day
  '11n': '⛈️', // thunderstorm night
  
  // Snow
  '13d': '🌨️', // snow day
  '13n': '🌨️', // snow night
  
  // Mist
  '50d': '🌫️', // mist day
  '50n': '🌫️', // mist night
};

// Weather condition to background gradient mapping
export const weatherBackgrounds = {
  Clear: 'bg-sunny',
  Clouds: 'bg-cloudy',
  Rain: 'bg-rainy',
  Drizzle: 'bg-rainy',
  Thunderstorm: 'bg-cloudy',
  Snow: 'bg-snowy',
  Mist: 'bg-cloudy',
  Smoke: 'bg-cloudy',
  Haze: 'bg-cloudy',
  Dust: 'bg-cloudy',
  Fog: 'bg-cloudy',
  Sand: 'bg-cloudy',
  Ash: 'bg-cloudy',
  Squall: 'bg-cloudy',
  Tornado: 'bg-cloudy',
};

// Weather condition to text color mapping
export const weatherTextColors = {
  Clear: 'text-yellow-800 dark:text-yellow-200',
  Clouds: 'text-gray-700 dark:text-gray-300',
  Rain: 'text-blue-800 dark:text-blue-200',
  Drizzle: 'text-blue-700 dark:text-blue-300',
  Thunderstorm: 'text-purple-800 dark:text-purple-200',
  Snow: 'text-blue-900 dark:text-blue-100',
  Mist: 'text-gray-600 dark:text-gray-400',
  Smoke: 'text-gray-600 dark:text-gray-400',
  Haze: 'text-gray-600 dark:text-gray-400',
  Dust: 'text-yellow-700 dark:text-yellow-300',
  Fog: 'text-gray-600 dark:text-gray-400',
  Sand: 'text-yellow-700 dark:text-yellow-300',
  Ash: 'text-gray-700 dark:text-gray-300',
  Squall: 'text-gray-700 dark:text-gray-300',
  Tornado: 'text-red-800 dark:text-red-200',
};

/**
 * Get weather icon for a given weather code
 * @param {string} iconCode - Weather icon code from API
 * @returns {string} Emoji icon
 */
export const getWeatherIcon = (iconCode) => {
  return weatherIcons[iconCode] || '🌤️';
};

/**
 * Get background class for weather condition
 * @param {string} condition - Weather condition
 * @param {boolean} isNight - Whether it's night time
 * @returns {string} CSS class for background
 */
export const getWeatherBackground = (condition, isNight = false) => {
  if (isNight) {
    return 'bg-night';
  }
  return weatherBackgrounds[condition] || 'bg-sunny';
};

/**
 * Get text color class for weather condition
 * @param {string} condition - Weather condition
 * @returns {string} CSS class for text color
 */
export const getWeatherTextColor = (condition) => {
  return weatherTextColors[condition] || 'text-gray-700 dark:text-gray-300';
};

/**
 * Check if it's currently night time based on sunrise/sunset
 * @param {number} currentTime - Current timestamp
 * @param {number} sunrise - Sunrise timestamp
 * @param {number} sunset - Sunset timestamp
 * @returns {boolean} True if it's night time
 */
export const isNightTime = (currentTime, sunrise, sunset) => {
  return currentTime < sunrise || currentTime > sunset;
};

/**
 * Get weather condition description with appropriate styling
 * @param {string} condition - Weather condition
 * @param {string} description - Weather description
 * @returns {Object} Styled weather info
 */
export const getStyledWeatherInfo = (condition, description) => {
  return {
    condition,
    description: description.charAt(0).toUpperCase() + description.slice(1),
    textColor: getWeatherTextColor(condition),
    icon: getWeatherIcon(condition)
  };
};
