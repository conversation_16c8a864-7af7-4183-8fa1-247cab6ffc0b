import { format, fromUnixTime, isToday, isTomorrow } from 'date-fns';

/**
 * Format timestamp to readable time
 * @param {number} timestamp - Unix timestamp
 * @param {string} formatString - Date format string
 * @returns {string} Formatted date string
 */
export const formatTime = (timestamp, formatString = 'HH:mm') => {
  return format(fromUnixTime(timestamp), formatString);
};

/**
 * Format timestamp to readable date
 * @param {number} timestamp - Unix timestamp
 * @param {string} formatString - Date format string
 * @returns {string} Formatted date string
 */
export const formatDate = (timestamp, formatString = 'MMM dd, yyyy') => {
  return format(fromUnixTime(timestamp), formatString);
};

/**
 * Get day name from timestamp
 * @param {number} timestamp - Unix timestamp
 * @returns {string} Day name (Today, Tomorrow, or day of week)
 */
export const getDayName = (timestamp) => {
  const date = fromUnixTime(timestamp);
  
  if (isToday(date)) {
    return 'Today';
  }
  
  if (isTomorrow(date)) {
    return 'Tomorrow';
  }
  
  return format(date, 'EEEE');
};

/**
 * Get short day name from timestamp
 * @param {number} timestamp - Unix timestamp
 * @returns {string} Short day name (Today, Tomorrow, or abbreviated day)
 */
export const getShortDayName = (timestamp) => {
  const date = fromUnixTime(timestamp);
  
  if (isToday(date)) {
    return 'Today';
  }
  
  if (isTomorrow(date)) {
    return 'Tomorrow';
  }
  
  return format(date, 'EEE');
};

/**
 * Format sunrise/sunset time
 * @param {number} timestamp - Unix timestamp
 * @returns {string} Formatted time string
 */
export const formatSunTime = (timestamp) => {
  return format(fromUnixTime(timestamp), 'h:mm a');
};

/**
 * Get current timestamp
 * @returns {number} Current Unix timestamp
 */
export const getCurrentTimestamp = () => {
  return Math.floor(Date.now() / 1000);
};

/**
 * Check if timestamp is in the past
 * @param {number} timestamp - Unix timestamp
 * @returns {boolean} True if timestamp is in the past
 */
export const isPast = (timestamp) => {
  return timestamp < getCurrentTimestamp();
};

/**
 * Get relative time string
 * @param {number} timestamp - Unix timestamp
 * @returns {string} Relative time string
 */
export const getRelativeTime = (timestamp) => {
  const now = getCurrentTimestamp();
  const diff = timestamp - now;
  const hours = Math.floor(diff / 3600);
  const minutes = Math.floor((diff % 3600) / 60);
  
  if (diff < 0) {
    return 'Past';
  }
  
  if (hours === 0) {
    return `${minutes}m`;
  }
  
  if (hours < 24) {
    return `${hours}h`;
  }
  
  const days = Math.floor(hours / 24);
  return `${days}d`;
};

/**
 * Format last updated time
 * @param {number} timestamp - Unix timestamp
 * @returns {string} Last updated string
 */
export const formatLastUpdated = (timestamp) => {
  const now = getCurrentTimestamp();
  const diff = now - timestamp;
  
  if (diff < 60) {
    return 'Just now';
  }
  
  if (diff < 3600) {
    const minutes = Math.floor(diff / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  }
  
  if (diff < 86400) {
    const hours = Math.floor(diff / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  }
  
  return formatDate(timestamp, 'MMM dd, h:mm a');
};
