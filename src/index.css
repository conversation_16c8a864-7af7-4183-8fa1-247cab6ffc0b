@import "tailwindcss";

/* Custom animations */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes bounceGentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

@keyframes pulseSlow {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Base styles */
html {
  font-family: 'Inter', system-ui, sans-serif;
}

body {
  background: linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%);
  min-height: 100vh;
  transition: all 0.3s ease;
}

body.dark {
  background: linear-gradient(135deg, #111827 0%, #1f2937 100%);
}

/* Component styles */
.weather-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(16px);
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.weather-card:hover {
  box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
}

.dark .weather-card {
  background: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(75, 85, 99, 0.2);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  transform: scale(1);
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #4f46e5 100%);
  transform: scale(1.05);
}

.btn-primary:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: #374151;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.3);
}

.dark .btn-secondary {
  background: rgba(75, 85, 99, 0.5);
  color: #d1d5db;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.dark .btn-secondary:hover {
  background: rgba(107, 114, 128, 0.5);
}

.input-field {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.input-field:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .input-field {
  background: rgba(31, 41, 55, 0.8);
  border-color: #4b5563;
  color: #f9fafb;
}

.dark .input-field:focus {
  border-color: #3b82f6;
}

.weather-icon {
  width: 4rem;
  height: 4rem;
  filter: drop-shadow(0 10px 8px rgba(0, 0, 0, 0.04));
  animation: bounceGentle 2s infinite;
}

.temperature-text {
  font-size: 2.25rem;
  font-weight: 700;
  background: linear-gradient(135deg, #2563eb 0%, #4f46e5 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.dark .temperature-text {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  background-clip: text;
  -webkit-background-clip: text;
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-bounce-gentle {
  animation: bounceGentle 2s infinite;
}

.animate-pulse-slow {
  animation: pulseSlow 3s infinite;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

/* Utility classes */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
