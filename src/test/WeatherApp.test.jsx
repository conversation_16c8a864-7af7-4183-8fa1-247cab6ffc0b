import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import WeatherApp from '../components/WeatherApp';

// Mock the geolocation API
const mockGeolocation = {
  getCurrentPosition: vi.fn(),
  watchPosition: vi.fn(),
  clearWatch: vi.fn()
};

Object.defineProperty(global.navigator, 'geolocation', {
  value: mockGeolocation,
  writable: true
});

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('WeatherApp', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  it('renders the main weather app', () => {
    render(<WeatherApp />);
    
    // Check if the main elements are present
    expect(screen.getByText('Weather App')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search for a city...')).toBeInTheDocument();
    expect(screen.getByText('Welcome to Weather App')).toBeInTheDocument();
  });

  it('displays welcome message when no weather data is loaded', () => {
    render(<WeatherApp />);
    
    expect(screen.getByText('Welcome to Weather App')).toBeInTheDocument();
    expect(screen.getByText('Search for a city or use your current location to get started.')).toBeInTheDocument();
  });

  it('has working tab navigation', () => {
    render(<WeatherApp />);
    
    const currentTab = screen.getByRole('button', { name: /current/i });
    const forecastTab = screen.getByRole('button', { name: /forecast/i });
    const favoritesTab = screen.getByRole('button', { name: /favorites/i });
    
    expect(currentTab).toBeInTheDocument();
    expect(forecastTab).toBeInTheDocument();
    expect(favoritesTab).toBeInTheDocument();
    
    // Click on forecast tab
    fireEvent.click(forecastTab);
    expect(screen.getByText('No Forecast Data')).toBeInTheDocument();
    
    // Click on favorites tab
    fireEvent.click(favoritesTab);
    expect(screen.getByText('No Favorite Locations')).toBeInTheDocument();
  });

  it('has a working theme toggle', () => {
    render(<WeatherApp />);
    
    const themeToggle = screen.getByLabelText(/switch to dark mode/i);
    expect(themeToggle).toBeInTheDocument();
    
    // Click theme toggle
    fireEvent.click(themeToggle);
    
    // Should save to localStorage
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'weather-theme',
      JSON.stringify('dark')
    );
  });

  it('can search for weather', async () => {
    render(<WeatherApp />);
    
    const searchInput = screen.getByPlaceholderText('Search for a city...');
    
    // Type in search input
    fireEvent.change(searchInput, { target: { value: 'London' } });
    expect(searchInput.value).toBe('London');
    
    // Submit search
    fireEvent.submit(searchInput.closest('form'));
    
    // Should show loading state
    await waitFor(() => {
      expect(screen.getByText(/fetching weather data/i)).toBeInTheDocument();
    });
  });

  it('handles location search', () => {
    render(<WeatherApp />);
    
    const locationButton = screen.getByLabelText('Use current location');
    expect(locationButton).toBeInTheDocument();
    
    // Mock successful geolocation
    mockGeolocation.getCurrentPosition.mockImplementationOnce((success) => {
      success({
        coords: {
          latitude: 51.5074,
          longitude: -0.1278,
          accuracy: 100
        },
        timestamp: Date.now()
      });
    });
    
    fireEvent.click(locationButton);
    
    expect(mockGeolocation.getCurrentPosition).toHaveBeenCalled();
  });
});

// Integration test for the complete weather flow
describe('WeatherApp Integration', () => {
  it('completes a full weather search flow', async () => {
    render(<WeatherApp />);
    
    // Start with welcome screen
    expect(screen.getByText('Welcome to Weather App')).toBeInTheDocument();
    
    // Search for a city
    const searchInput = screen.getByPlaceholderText('Search for a city...');
    fireEvent.change(searchInput, { target: { value: 'New York' } });
    fireEvent.submit(searchInput.closest('form'));
    
    // Should show loading
    await waitFor(() => {
      expect(screen.getByText(/fetching weather data/i)).toBeInTheDocument();
    });
    
    // After loading, should show weather data (mocked)
    await waitFor(() => {
      expect(screen.getByText('New York')).toBeInTheDocument();
    }, { timeout: 3000 });
    
    // Should be able to switch to forecast tab
    const forecastTab = screen.getByRole('button', { name: /forecast/i });
    fireEvent.click(forecastTab);
    
    await waitFor(() => {
      expect(screen.getByText('5-Day Forecast')).toBeInTheDocument();
    });
  });
});
