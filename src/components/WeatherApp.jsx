import React, { useState, useEffect } from 'react';
import {
  CloudIcon,
  Cog6ToothIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

import SearchBar from './SearchBar';
import CurrentWeather from './CurrentWeather';
import { ForecastList } from './ForecastCard';
import FavoriteLocations, { QuickFavorites } from './FavoriteLocations';
import ThemeToggle, { AnimatedThemeToggle } from './ThemeToggle';
import LoadingSpinner, { WeatherLoadingSpinner, WeatherCardSkeleton, ForecastSkeleton } from './LoadingSpinner';
import ErrorMessage, { WeatherError, NetworkError } from './ErrorMessage';

import { useWeatherData } from '../hooks/useWeatherData';
import { useGeolocation } from '../hooks/useGeolocation';
import { useTheme } from '../hooks/useLocalStorage';

/**
 * Main Weather App component
 * @returns {JSX.Element} Weather app component
 */
const WeatherApp = () => {
  const [activeTab, setActiveTab] = useState('current'); // 'current', 'forecast', 'favorites'
  const [showSettings, setShowSettings] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  const { theme } = useTheme();
  const { location, getCurrentPosition, loading: locationLoading } = useGeolocation();

  const {
    currentWeather,
    forecast,
    loading,
    error,
    lastUpdated,
    currentCity,
    fetchWeatherByCity,
    fetchWeatherByCoords,
    refreshWeather,
    clearWeatherData
  } = useWeatherData();

  // Monitor network status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Handle location-based weather search
  const handleLocationSearch = async () => {
    try {
      await getCurrentPosition();
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  // Fetch weather when location is obtained
  useEffect(() => {
    if (location) {
      fetchWeatherByCoords(location.latitude, location.longitude);
    }
  }, [location, fetchWeatherByCoords]);

  // Handle city search
  const handleCitySearch = (city) => {
    fetchWeatherByCity(city);
    setActiveTab('current');
  };

  // Handle favorite location selection
  const handleFavoriteSelect = (city) => {
    fetchWeatherByCity(city);
    setActiveTab('current');
  };

  // Handle retry
  const handleRetry = () => {
    if (currentCity) {
      refreshWeather();
    }
  };

  const tabs = [
    { id: 'current', label: 'Current', icon: CloudIcon },
    { id: 'forecast', label: 'Forecast', icon: CloudIcon },
    { id: 'favorites', label: 'Favorites', icon: CloudIcon }
  ];

  return (
    <div className="min-h-screen transition-colors duration-300">
      <NetworkError isOnline={isOnline} />

      {/* Header */}
      <header className="bg-white/90 dark:bg-gray-800/80 backdrop-blur-lg border-b border-gray-200/30 dark:border-gray-700/20 sticky top-0 z-40 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                <CloudIcon className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                Weather App
              </h1>
            </div>

            {/* Search Bar */}
            <div className="flex-1 max-w-md mx-8">
              <SearchBar
                onSearch={handleCitySearch}
                onLocationSearch={handleLocationSearch}
                loading={loading || locationLoading}
                placeholder="Search for a city..."
              />
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-3">
              <AnimatedThemeToggle />

              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-2 rounded-lg text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200"
                aria-label="Settings"
              >
                <Cog6ToothIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <aside className="lg:col-span-1 space-y-6">
            {/* Quick Favorites */}
            <div className="weather-card p-4">
              <QuickFavorites onLocationSelect={handleFavoriteSelect} />
            </div>

            {/* Weather Info */}
            {currentWeather && (
              <div className="weather-card p-4">
                <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center space-x-1">
                  <InformationCircleIcon className="h-4 w-4" />
                  <span>Quick Info</span>
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Location:</span>
                    <span className="text-gray-900 dark:text-white font-medium">
                      {currentWeather.name}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Condition:</span>
                    <span className="text-gray-900 dark:text-white font-medium capitalize">
                      {currentWeather.weather[0].description}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Humidity:</span>
                    <span className="text-gray-900 dark:text-white font-medium">
                      {currentWeather.main.humidity}%
                    </span>
                  </div>
                </div>
              </div>
            )}
          </aside>

          {/* Main Content Area */}
          <div className="lg:col-span-3 space-y-8">
            {/* Tab Navigation */}
            <div className="flex space-x-1 bg-white/70 dark:bg-gray-800/50 p-1 rounded-xl backdrop-blur-sm border border-gray-200/30 dark:border-gray-700/20">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg font-medium transition-all duration-200
                    ${activeTab === tab.id
                      ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-md'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                    }
                  `}
                >
                  <tab.icon className="h-5 w-5" />
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div className="space-y-8">
              {/* Current Weather Tab */}
              {activeTab === 'current' && (
                <div className="space-y-6">
                  {loading && !currentWeather && (
                    <WeatherCardSkeleton />
                  )}

                  {error && !currentWeather && (
                    <WeatherError
                      error={error}
                      onRetry={handleRetry}
                      city={currentCity}
                    />
                  )}

                  {currentWeather && (
                    <CurrentWeather
                      weather={currentWeather}
                      onRefresh={refreshWeather}
                      loading={loading}
                      lastUpdated={lastUpdated}
                    />
                  )}

                  {!currentWeather && !loading && !error && (
                    <div className="weather-card p-12 text-center">
                      <CloudIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                        Welcome to Weather App
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-6">
                        Search for a city or use your current location to get started.
                      </p>
                      <button
                        onClick={handleLocationSearch}
                        disabled={locationLoading}
                        className="btn-primary"
                      >
                        {locationLoading ? 'Getting Location...' : 'Use Current Location'}
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Forecast Tab */}
              {activeTab === 'forecast' && (
                <div className="space-y-6">
                  {loading && !forecast && (
                    <ForecastSkeleton />
                  )}

                  {error && !forecast && (
                    <WeatherError
                      error={error}
                      onRetry={handleRetry}
                      city={currentCity}
                    />
                  )}

                  {forecast && (
                    <ForecastList forecast={forecast} loading={loading} />
                  )}

                  {!forecast && !loading && !error && (
                    <div className="weather-card p-12 text-center">
                      <CloudIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                        No Forecast Data
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        Search for a location to view the 5-day forecast.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Favorites Tab */}
              {activeTab === 'favorites' && (
                <FavoriteLocations onLocationSelect={handleFavoriteSelect} />
              )}
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white/70 dark:bg-gray-800/50 backdrop-blur-sm border-t border-gray-200/30 dark:border-gray-700/20 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-gray-600 dark:text-gray-400">
            <p className="text-sm">
              Weather data provided by OpenWeatherMap API • Built with React & Tailwind CSS
            </p>
            <p className="text-xs mt-1">
              Auto-refreshes every 30 seconds • {theme === 'dark' ? '🌙' : '☀️'} {theme} mode
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default WeatherApp;
