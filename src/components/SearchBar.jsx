import React, { useState, useRef, useEffect } from 'react';
import { 
  MagnifyingGlassIcon, 
  MapPinIcon, 
  XMarkIcon 
} from '@heroicons/react/24/outline';
import { searchCities } from '../utils/weatherApi';
import { useGeolocation } from '../hooks/useGeolocation';

/**
 * Search bar component for city weather search
 * @param {Object} props - Component props
 * @param {Function} props.onSearch - Search callback function
 * @param {Function} props.onLocationSearch - Location search callback
 * @param {boolean} props.loading - Loading state
 * @param {string} props.placeholder - Input placeholder text
 * @param {string} props.initialValue - Initial search value
 * @returns {JSX.Element} Search bar component
 */
const SearchBar = ({ 
  onSearch, 
  onLocationSearch, 
  loading = false, 
  placeholder = "Search for a city...",
  initialValue = ""
}) => {
  const [query, setQuery] = useState(initialValue);
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [isSearching, setIsSearching] = useState(false);
  
  const inputRef = useRef(null);
  const suggestionsRef = useRef(null);
  
  const { getCurrentPosition, loading: locationLoading, error: locationError } = useGeolocation();

  // Debounced search for city suggestions
  useEffect(() => {
    if (query.length < 2) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    const timeoutId = setTimeout(async () => {
      setIsSearching(true);
      try {
        const cities = await searchCities(query);
        setSuggestions(cities);
        setShowSuggestions(cities.length > 0);
      } catch (error) {
        console.error('Error searching cities:', error);
        setSuggestions([]);
        setShowSuggestions(false);
      } finally {
        setIsSearching(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query]);

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    if (query.trim() && onSearch) {
      onSearch(query.trim());
      setShowSuggestions(false);
      setSelectedIndex(-1);
    }
  };

  // Handle input change
  const handleInputChange = (e) => {
    setQuery(e.target.value);
    setSelectedIndex(-1);
  };

  // Handle suggestion selection
  const handleSuggestionClick = (city) => {
    setQuery(city);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    if (onSearch) {
      onSearch(city);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (!showSuggestions || suggestions.length === 0) {
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          handleSuggestionClick(suggestions[selectedIndex]);
        } else {
          handleSubmit(e);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle location search
  const handleLocationSearch = async () => {
    try {
      await getCurrentPosition();
      if (onLocationSearch) {
        onLocationSearch();
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  // Clear search
  const clearSearch = () => {
    setQuery('');
    setSuggestions([]);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        suggestionsRef.current && 
        !suggestionsRef.current.contains(event.target) &&
        !inputRef.current?.contains(event.target)
      ) {
        setShowSuggestions(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="relative w-full max-w-md mx-auto">
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => suggestions.length > 0 && setShowSuggestions(true)}
            placeholder={placeholder}
            disabled={loading}
            className={`
              input-field pr-20 pl-10
              ${loading ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          />
          
          {/* Search icon */}
          <MagnifyingGlassIcon 
            className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" 
          />
          
          {/* Action buttons */}
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
            {query && (
              <button
                type="button"
                onClick={clearSearch}
                className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
                aria-label="Clear search"
              >
                <XMarkIcon className="h-4 w-4 text-gray-400" />
              </button>
            )}
            
            {onLocationSearch && (
              <button
                type="button"
                onClick={handleLocationSearch}
                disabled={locationLoading}
                className={`
                  p-1.5 rounded-full transition-all duration-200 transform hover:scale-105
                  ${locationLoading 
                    ? 'opacity-50 cursor-not-allowed' 
                    : 'hover:bg-blue-100 dark:hover:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                  }
                `}
                aria-label="Use current location"
                title="Use current location"
              >
                <MapPinIcon className={`h-4 w-4 ${locationLoading ? 'animate-pulse' : ''}`} />
              </button>
            )}
          </div>
        </div>
        
        {/* Submit button (hidden, form submits on Enter) */}
        <button type="submit" className="sr-only">
          Search
        </button>
      </form>

      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div 
          ref={suggestionsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 z-50 max-h-60 overflow-y-auto"
        >
          {suggestions.map((city, index) => (
            <button
              key={city}
              onClick={() => handleSuggestionClick(city)}
              className={`
                w-full text-left px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 
                transition-colors duration-150 border-b border-gray-100 dark:border-gray-700 last:border-b-0
                ${index === selectedIndex ? 'bg-blue-50 dark:bg-blue-900/30' : ''}
              `}
            >
              <div className="flex items-center space-x-2">
                <MapPinIcon className="h-4 w-4 text-gray-400" />
                <span className="text-gray-900 dark:text-gray-100">{city}</span>
              </div>
            </button>
          ))}
          
          {isSearching && (
            <div className="px-4 py-3 text-center text-gray-500 dark:text-gray-400">
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full" />
                <span>Searching...</span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Location error */}
      {locationError && (
        <div className="absolute top-full left-0 right-0 mt-1 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-sm text-red-700 dark:text-red-300">
          {locationError.message}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
