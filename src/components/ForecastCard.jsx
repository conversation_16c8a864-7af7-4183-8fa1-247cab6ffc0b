import React from 'react';
import { getWeatherIcon } from '../utils/weatherIcons';
import { getDayName, getShortDayName, formatTime } from '../utils/dateUtils';

/**
 * Individual forecast card component
 * @param {Object} props - Component props
 * @param {Object} props.forecast - Forecast data
 * @param {boolean} props.compact - Whether to show compact version
 * @param {boolean} props.showTime - Whether to show time
 * @returns {JSX.Element} Forecast card component
 */
const ForecastCard = ({ forecast, compact = false, showTime = false }) => {
  if (!forecast) return null;

  const {
    dt,
    main: { temp, temp_min, temp_max, humidity } = {},
    weather: [weatherInfo] = [{}],
    wind: { speed } = {}
  } = forecast;

  const weatherIcon = getWeatherIcon(weatherInfo.icon);
  const dayName = compact ? getShortDayName(dt) : getDayName(dt);

  if (compact) {
    return (
      <div className="weather-card p-4 text-center hover:scale-105 transition-transform duration-200">
        <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
          {dayName}
        </p>
        
        {showTime && (
          <p className="text-xs text-gray-500 dark:text-gray-500 mb-2">
            {formatTime(dt)}
          </p>
        )}
        
        <div className="text-3xl mb-3 animate-bounce-gentle">
          {weatherIcon}
        </div>
        
        <div className="space-y-1">
          <p className="text-lg font-bold text-gray-900 dark:text-white">
            {Math.round(temp || temp_max)}°
          </p>
          {temp_min && temp_max && (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {Math.round(temp_min)}° / {Math.round(temp_max)}°
            </p>
          )}
        </div>
        
        <p className="text-xs text-gray-600 dark:text-gray-400 mt-2 capitalize">
          {weatherInfo.description}
        </p>
      </div>
    );
  }

  return (
    <div className="weather-card p-6 hover:shadow-2xl transition-all duration-300">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {dayName}
          </h3>
          {showTime && (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {formatTime(dt)}
            </p>
          )}
        </div>
        
        <div className="text-4xl animate-bounce-gentle">
          {weatherIcon}
        </div>
      </div>

      <div className="flex items-center justify-between mb-4">
        <div>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {Math.round(temp || temp_max)}°C
          </p>
          {temp_min && temp_max && (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Low: {Math.round(temp_min)}°C
            </p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">
          {weatherInfo.description}
        </p>
        
        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
          <span>💧 {humidity}%</span>
          <span>🌪️ {speed} m/s</span>
        </div>
      </div>
    </div>
  );
};

/**
 * 5-day forecast component
 * @param {Object} props - Component props
 * @param {Object} props.forecast - Forecast data from API
 * @param {boolean} props.loading - Loading state
 * @returns {JSX.Element} Forecast component
 */
export const ForecastList = ({ forecast, loading = false }) => {
  if (!forecast || !forecast.list) return null;

  // Group forecast by day and take one entry per day (around noon)
  const dailyForecasts = forecast.list.reduce((acc, item) => {
    const date = new Date(item.dt * 1000);
    const dateKey = date.toDateString();
    
    // Prefer forecasts around noon (12:00)
    if (!acc[dateKey] || Math.abs(date.getHours() - 12) < Math.abs(new Date(acc[dateKey].dt * 1000).getHours() - 12)) {
      acc[dateKey] = item;
    }
    
    return acc;
  }, {});

  const forecasts = Object.values(dailyForecasts).slice(0, 5);

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white text-center">
        5-Day Forecast
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {forecasts.map((item, index) => (
          <ForecastCard 
            key={item.dt} 
            forecast={item} 
            compact={true}
          />
        ))}
      </div>
    </div>
  );
};

/**
 * Hourly forecast component
 * @param {Object} props - Component props
 * @param {Object} props.forecast - Forecast data from API
 * @param {number} props.hours - Number of hours to show (default: 24)
 * @returns {JSX.Element} Hourly forecast component
 */
export const HourlyForecast = ({ forecast, hours = 24 }) => {
  if (!forecast || !forecast.list) return null;

  const hourlyForecasts = forecast.list.slice(0, Math.min(hours / 3, forecast.list.length));

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
        Hourly Forecast
      </h3>
      
      <div className="flex space-x-4 overflow-x-auto pb-4">
        {hourlyForecasts.map((item) => (
          <div 
            key={item.dt}
            className="flex-shrink-0 weather-card p-4 text-center min-w-[120px]"
          >
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
              {formatTime(item.dt)}
            </p>
            
            <div className="text-2xl mb-2">
              {getWeatherIcon(item.weather[0].icon)}
            </div>
            
            <p className="text-lg font-bold text-gray-900 dark:text-white mb-1">
              {Math.round(item.main.temp)}°
            </p>
            
            <p className="text-xs text-gray-500 dark:text-gray-400">
              💧 {item.main.humidity}%
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * Forecast summary component
 * @param {Object} props - Component props
 * @param {Object} props.forecast - Forecast data
 * @returns {JSX.Element} Forecast summary component
 */
export const ForecastSummary = ({ forecast }) => {
  if (!forecast || !forecast.list) return null;

  const today = forecast.list[0];
  const tomorrow = forecast.list.find(item => {
    const itemDate = new Date(item.dt * 1000);
    const tomorrowDate = new Date();
    tomorrowDate.setDate(tomorrowDate.getDate() + 1);
    return itemDate.toDateString() === tomorrowDate.toDateString();
  });

  return (
    <div className="weather-card p-6">
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
        Forecast Summary
      </h3>
      
      <div className="space-y-4">
        {today && (
          <div className="flex items-center justify-between">
            <span className="text-gray-600 dark:text-gray-400">Today</span>
            <div className="flex items-center space-x-2">
              <span className="text-2xl">{getWeatherIcon(today.weather[0].icon)}</span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {Math.round(today.main.temp)}°C
              </span>
            </div>
          </div>
        )}
        
        {tomorrow && (
          <div className="flex items-center justify-between">
            <span className="text-gray-600 dark:text-gray-400">Tomorrow</span>
            <div className="flex items-center space-x-2">
              <span className="text-2xl">{getWeatherIcon(tomorrow.weather[0].icon)}</span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {Math.round(tomorrow.main.temp)}°C
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ForecastCard;
