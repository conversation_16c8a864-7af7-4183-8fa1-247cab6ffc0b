import React, { useState } from 'react';
import { 
  HeartIcon, 
  TrashIcon, 
  MapPinIcon,
  PlusIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { useFavorites } from '../hooks/useLocalStorage';
import { useMultipleWeatherData } from '../hooks/useWeatherData';
import { getWeatherIcon } from '../utils/weatherIcons';
import LoadingSpinner from './LoadingSpinner';

/**
 * Individual favorite location card
 * @param {Object} props - Component props
 * @param {Object} props.favorite - Favorite location data
 * @param {Function} props.onSelect - Selection callback
 * @param {Function} props.onRemove - Remove callback
 * @param {Object} props.weatherData - Current weather data for location
 * @param {boolean} props.loading - Loading state
 * @returns {JSX.Element} Favorite location card
 */
const FavoriteLocationCard = ({ favorite, onSelect, onRemove, weatherData, loading }) => {
  const handleSelect = () => {
    if (onSelect) {
      onSelect(favorite.name);
    }
  };

  const handleRemove = (e) => {
    e.stopPropagation();
    if (onRemove) {
      onRemove(favorite.name);
    }
  };

  return (
    <div 
      className="weather-card p-4 cursor-pointer hover:scale-105 transition-all duration-200 relative group"
      onClick={handleSelect}
    >
      {/* Remove button */}
      <button
        onClick={handleRemove}
        className="absolute top-2 right-2 p-1 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-200 dark:hover:bg-red-900/50"
        aria-label={`Remove ${favorite.name} from favorites`}
      >
        <XMarkIcon className="h-4 w-4" />
      </button>

      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <MapPinIcon className="h-4 w-4 text-gray-500 dark:text-gray-400" />
          <h3 className="font-semibold text-gray-900 dark:text-white truncate">
            {favorite.name}
          </h3>
        </div>
        <HeartSolidIcon className="h-5 w-5 text-red-500" />
      </div>

      {loading ? (
        <div className="flex justify-center py-4">
          <LoadingSpinner size="sm" />
        </div>
      ) : weatherData ? (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <span className="text-2xl">
              {getWeatherIcon(weatherData.weather[0].icon)}
            </span>
            <div>
              <p className="text-lg font-bold text-gray-900 dark:text-white">
                {Math.round(weatherData.main.temp)}°C
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                {weatherData.weather[0].description}
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-2">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Click to view weather
          </p>
        </div>
      )}
    </div>
  );
};

/**
 * Favorite locations management component
 * @param {Object} props - Component props
 * @param {Function} props.onLocationSelect - Location selection callback
 * @param {boolean} props.showWeather - Whether to show weather data for favorites
 * @returns {JSX.Element} Favorite locations component
 */
const FavoriteLocations = ({ onLocationSelect, showWeather = true }) => {
  const { favorites, removeFavorite, clearFavorites } = useFavorites();
  const { locations, loading, addLocation, removeLocation } = useMultipleWeatherData();
  const [showAll, setShowAll] = useState(false);

  // Load weather data for favorites when component mounts
  React.useEffect(() => {
    if (showWeather && favorites.length > 0) {
      favorites.forEach(favorite => {
        addLocation(favorite.name);
      });
    }
  }, [favorites, showWeather, addLocation]);

  const handleLocationSelect = (cityName) => {
    if (onLocationSelect) {
      onLocationSelect(cityName);
    }
  };

  const handleRemoveFavorite = (cityName) => {
    removeFavorite(cityName);
    removeLocation(cityName);
  };

  const handleClearAll = () => {
    if (window.confirm('Are you sure you want to remove all favorite locations?')) {
      clearFavorites();
      locations.forEach(location => {
        removeLocation(location.city);
      });
    }
  };

  const getWeatherForLocation = (cityName) => {
    return locations.find(location => location.city === cityName)?.weather;
  };

  const displayedFavorites = showAll ? favorites : favorites.slice(0, 6);

  if (favorites.length === 0) {
    return (
      <div className="weather-card p-8 text-center">
        <HeartIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          No Favorite Locations
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Add locations to your favorites by clicking the heart icon when viewing weather data.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center space-x-2">
          <HeartSolidIcon className="h-6 w-6 text-red-500" />
          <span>Favorite Locations</span>
          <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
            ({favorites.length})
          </span>
        </h2>

        {favorites.length > 0 && (
          <div className="flex items-center space-x-2">
            {favorites.length > 6 && (
              <button
                onClick={() => setShowAll(!showAll)}
                className="btn-secondary text-sm"
              >
                {showAll ? 'Show Less' : `Show All (${favorites.length})`}
              </button>
            )}
            
            <button
              onClick={handleClearAll}
              className="p-2 rounded-lg text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors duration-200"
              aria-label="Clear all favorites"
              title="Clear all favorites"
            >
              <TrashIcon className="h-5 w-5" />
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {displayedFavorites.map((favorite) => (
          <FavoriteLocationCard
            key={favorite.id}
            favorite={favorite}
            onSelect={handleLocationSelect}
            onRemove={handleRemoveFavorite}
            weatherData={showWeather ? getWeatherForLocation(favorite.name) : null}
            loading={loading}
          />
        ))}
      </div>

      {favorites.length > 6 && !showAll && (
        <div className="text-center">
          <button
            onClick={() => setShowAll(true)}
            className="btn-secondary"
          >
            Show {favorites.length - 6} More Locations
          </button>
        </div>
      )}
    </div>
  );
};

/**
 * Quick favorites component for header/sidebar
 * @param {Object} props - Component props
 * @param {Function} props.onLocationSelect - Location selection callback
 * @param {number} props.maxItems - Maximum items to show
 * @returns {JSX.Element} Quick favorites component
 */
export const QuickFavorites = ({ onLocationSelect, maxItems = 3 }) => {
  const { favorites } = useFavorites();

  if (favorites.length === 0) return null;

  const displayedFavorites = favorites.slice(0, maxItems);

  return (
    <div className="space-y-2">
      <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center space-x-1">
        <HeartSolidIcon className="h-4 w-4 text-red-500" />
        <span>Quick Access</span>
      </h3>
      
      <div className="space-y-1">
        {displayedFavorites.map((favorite) => (
          <button
            key={favorite.id}
            onClick={() => onLocationSelect(favorite.name)}
            className="w-full text-left px-3 py-2 rounded-lg text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 flex items-center space-x-2"
          >
            <MapPinIcon className="h-4 w-4" />
            <span className="truncate">{favorite.name}</span>
          </button>
        ))}
      </div>
      
      {favorites.length > maxItems && (
        <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
          +{favorites.length - maxItems} more
        </p>
      )}
    </div>
  );
};

export default FavoriteLocations;
