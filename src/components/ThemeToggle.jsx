import React from 'react';
import { SunIcon, MoonIcon } from '@heroicons/react/24/outline';
import { useTheme } from '../hooks/useLocalStorage';

/**
 * Theme toggle component for switching between light and dark modes
 * @param {Object} props - Component props
 * @param {string} props.size - Size of the toggle (sm, md, lg)
 * @param {string} props.variant - Style variant (button, switch)
 * @param {boolean} props.showLabel - Whether to show theme label
 * @returns {JSX.Element} Theme toggle component
 */
const ThemeToggle = ({ size = 'md', variant = 'button', showLabel = false }) => {
  const { theme, toggleTheme, isDark } = useTheme();

  const sizeClasses = {
    sm: {
      button: 'p-1.5',
      icon: 'h-4 w-4',
      text: 'text-xs'
    },
    md: {
      button: 'p-2',
      icon: 'h-5 w-5',
      text: 'text-sm'
    },
    lg: {
      button: 'p-3',
      icon: 'h-6 w-6',
      text: 'text-base'
    }
  };

  const sizes = sizeClasses[size];

  if (variant === 'switch') {
    return (
      <div className="flex items-center space-x-3">
        {showLabel && (
          <span className={`font-medium text-gray-700 dark:text-gray-300 ${sizes.text}`}>
            {isDark ? 'Dark' : 'Light'} Mode
          </span>
        )}
        
        <button
          onClick={toggleTheme}
          className={`
            relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200
            ${isDark 
              ? 'bg-blue-600 hover:bg-blue-700' 
              : 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600'
            }
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
          `}
          role="switch"
          aria-checked={isDark}
          aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
        >
          <span
            className={`
              inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200
              ${isDark ? 'translate-x-6' : 'translate-x-1'}
            `}
          />
          
          {/* Icons inside the switch */}
          <SunIcon 
            className={`
              absolute left-1 top-1 h-4 w-4 text-yellow-500 transition-opacity duration-200
              ${isDark ? 'opacity-0' : 'opacity-100'}
            `}
          />
          <MoonIcon 
            className={`
              absolute right-1 top-1 h-4 w-4 text-blue-300 transition-opacity duration-200
              ${isDark ? 'opacity-100' : 'opacity-0'}
            `}
          />
        </button>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      <button
        onClick={toggleTheme}
        className={`
          ${sizes.button} rounded-lg transition-all duration-200 transform hover:scale-105
          bg-white/20 hover:bg-white/30 dark:bg-gray-700/50 dark:hover:bg-gray-600/50
          backdrop-blur-sm border border-white/20 dark:border-gray-600/20
          text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
        `}
        aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
        title={`Switch to ${isDark ? 'light' : 'dark'} mode`}
      >
        {isDark ? (
          <SunIcon className={`${sizes.icon} transition-transform duration-200 hover:rotate-12`} />
        ) : (
          <MoonIcon className={`${sizes.icon} transition-transform duration-200 hover:-rotate-12`} />
        )}
      </button>
      
      {showLabel && (
        <span className={`font-medium text-gray-700 dark:text-gray-300 ${sizes.text}`}>
          {isDark ? 'Dark' : 'Light'}
        </span>
      )}
    </div>
  );
};

/**
 * Animated theme toggle with smooth transitions
 * @returns {JSX.Element} Animated theme toggle
 */
export const AnimatedThemeToggle = () => {
  const { isDark, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className={`
        relative p-2 rounded-full transition-all duration-300 transform hover:scale-110
        ${isDark 
          ? 'bg-gray-800 text-yellow-400 shadow-lg shadow-yellow-400/20' 
          : 'bg-yellow-400 text-gray-800 shadow-lg shadow-yellow-400/30'
        }
        focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
      `}
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
    >
      <div className="relative w-6 h-6">
        {/* Sun Icon */}
        <SunIcon 
          className={`
            absolute inset-0 w-6 h-6 transition-all duration-300 transform
            ${isDark 
              ? 'opacity-0 rotate-90 scale-0' 
              : 'opacity-100 rotate-0 scale-100'
            }
          `}
        />
        
        {/* Moon Icon */}
        <MoonIcon 
          className={`
            absolute inset-0 w-6 h-6 transition-all duration-300 transform
            ${isDark 
              ? 'opacity-100 rotate-0 scale-100' 
              : 'opacity-0 -rotate-90 scale-0'
            }
          `}
        />
      </div>
      
      {/* Glow effect */}
      <div 
        className={`
          absolute inset-0 rounded-full transition-opacity duration-300
          ${isDark 
            ? 'bg-yellow-400/20 opacity-100' 
            : 'bg-yellow-400/30 opacity-0'
          }
        `}
      />
    </button>
  );
};

/**
 * Compact theme toggle for mobile
 * @returns {JSX.Element} Compact theme toggle
 */
export const CompactThemeToggle = () => {
  const { isDark, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className="p-1.5 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-200"
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
    >
      {isDark ? (
        <SunIcon className="h-5 w-5" />
      ) : (
        <MoonIcon className="h-5 w-5" />
      )}
    </button>
  );
};

export default ThemeToggle;
