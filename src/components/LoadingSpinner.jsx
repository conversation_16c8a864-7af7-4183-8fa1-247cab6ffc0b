import React from 'react';

/**
 * Loading spinner component with different sizes and styles
 * @param {Object} props - Component props
 * @param {string} props.size - Size of spinner (sm, md, lg)
 * @param {string} props.color - Color theme (blue, white, gray)
 * @param {string} props.text - Loading text to display
 * @param {boolean} props.overlay - Whether to show as overlay
 * @returns {JSX.Element} Loading spinner component
 */
const LoadingSpinner = ({ 
  size = 'md', 
  color = 'blue', 
  text = 'Loading...', 
  overlay = false 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const colorClasses = {
    blue: 'text-blue-500',
    white: 'text-white',
    gray: 'text-gray-500'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  const spinnerContent = (
    <div className="flex flex-col items-center justify-center space-y-3">
      <div className={`animate-spin ${sizeClasses[size]} ${colorClasses[color]}`}>
        <svg 
          className="w-full h-full" 
          fill="none" 
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle 
            className="opacity-25" 
            cx="12" 
            cy="12" 
            r="10" 
            stroke="currentColor" 
            strokeWidth="4"
          />
          <path 
            className="opacity-75" 
            fill="currentColor" 
            d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      </div>
      {text && (
        <p className={`${textSizeClasses[size]} ${colorClasses[color]} font-medium animate-pulse`}>
          {text}
        </p>
      )}
    </div>
  );

  if (overlay) {
    return (
      <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-2xl">
          {spinnerContent}
        </div>
      </div>
    );
  }

  return spinnerContent;
};

/**
 * Weather-themed loading spinner
 * @param {Object} props - Component props
 * @param {string} props.text - Loading text
 * @returns {JSX.Element} Weather loading spinner
 */
export const WeatherLoadingSpinner = ({ text = 'Fetching weather data...' }) => {
  return (
    <div className="flex flex-col items-center justify-center space-y-4 p-8">
      <div className="relative">
        {/* Sun */}
        <div className="w-16 h-16 bg-yellow-400 rounded-full animate-pulse-slow">
          <div className="absolute inset-2 bg-yellow-300 rounded-full animate-bounce-gentle" />
        </div>
        
        {/* Rays */}
        <div className="absolute inset-0 animate-spin" style={{ animationDuration: '3s' }}>
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-4 bg-yellow-400 rounded-full"
              style={{
                top: '-8px',
                left: '50%',
                transformOrigin: '50% 40px',
                transform: `translateX(-50%) rotate(${i * 45}deg)`
              }}
            />
          ))}
        </div>
      </div>
      
      <p className="text-gray-600 dark:text-gray-300 font-medium animate-pulse">
        {text}
      </p>
    </div>
  );
};

/**
 * Skeleton loader for weather cards
 * @returns {JSX.Element} Skeleton loader component
 */
export const WeatherCardSkeleton = () => {
  return (
    <div className="weather-card p-6 animate-pulse">
      <div className="flex items-center justify-between mb-4">
        <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-32" />
        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-20" />
      </div>
      
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full" />
          <div>
            <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-20 mb-2" />
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-24" />
          </div>
        </div>
        <div className="text-right">
          <div className="h-12 bg-gray-300 dark:bg-gray-600 rounded w-24 mb-2" />
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-16" />
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded" />
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded flex-1" />
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * Skeleton loader for forecast cards
 * @returns {JSX.Element} Forecast skeleton loader
 */
export const ForecastSkeleton = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="weather-card p-4 animate-pulse">
          <div className="text-center">
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-16 mx-auto mb-3" />
            <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full mx-auto mb-3" />
            <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-12 mx-auto mb-2" />
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-20 mx-auto" />
          </div>
        </div>
      ))}
    </div>
  );
};

export default LoadingSpinner;
