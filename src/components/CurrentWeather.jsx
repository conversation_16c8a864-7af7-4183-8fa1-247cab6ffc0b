import React from 'react';
import { 
  HeartIcon,
  ArrowPathIcon,
  EyeIcon,
  SunIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { getWeatherIcon, getWeatherBackground, isNightTime } from '../utils/weatherIcons';
import { formatSunTime, formatLastUpdated } from '../utils/dateUtils';
import { useFavorites } from '../hooks/useLocalStorage';

/**
 * Current weather display component
 * @param {Object} props - Component props
 * @param {Object} props.weather - Weather data object
 * @param {Function} props.onRefresh - Refresh callback function
 * @param {boolean} props.loading - Loading state
 * @param {number} props.lastUpdated - Last updated timestamp
 * @returns {JSX.Element} Current weather component
 */
const CurrentWeather = ({ weather, onRefresh, loading = false, lastUpdated }) => {
  const { isFavorite, toggleFavorite } = useFavorites();

  if (!weather) return null;

  const {
    name,
    sys: { country, sunrise, sunset } = {},
    main: { temp, feels_like, humidity, pressure } = {},
    weather: [weatherInfo] = [{}],
    wind: { speed, deg } = {},
    visibility,
    dt
  } = weather;

  const isNight = isNightTime(dt, sunrise, sunset);
  const weatherIcon = getWeatherIcon(weatherInfo.icon);
  const backgroundClass = getWeatherBackground(weatherInfo.main, isNight);
  const isCityFavorite = isFavorite(name);

  const handleFavoriteToggle = () => {
    toggleFavorite({
      name,
      country,
      weather: weatherInfo,
      temp: Math.round(temp)
    });
  };

  const getWindDirection = (degrees) => {
    const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
    return directions[Math.round(degrees / 22.5) % 16];
  };

  return (
    <div className={`weather-card p-6 ${backgroundClass} relative overflow-hidden`}>
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-4 right-4 text-6xl">
          {weatherIcon}
        </div>
      </div>

      {/* Header */}
      <div className="relative flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {name}
            {country && (
              <span className="text-lg font-normal text-gray-600 dark:text-gray-300 ml-2">
                {country}
              </span>
            )}
          </h2>
          {lastUpdated && (
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Updated {formatLastUpdated(lastUpdated / 1000)}
            </p>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleFavoriteToggle}
            className={`
              p-2 rounded-full transition-all duration-200 transform hover:scale-110
              ${isCityFavorite 
                ? 'text-red-500 hover:text-red-600' 
                : 'text-gray-400 hover:text-red-500'
              }
            `}
            aria-label={isCityFavorite ? 'Remove from favorites' : 'Add to favorites'}
          >
            {isCityFavorite ? (
              <HeartSolidIcon className="h-6 w-6" />
            ) : (
              <HeartIcon className="h-6 w-6" />
            )}
          </button>

          <button
            onClick={onRefresh}
            disabled={loading}
            className={`
              p-2 rounded-full transition-all duration-200 transform hover:scale-110
              text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white
              ${loading ? 'animate-spin' : ''}
            `}
            aria-label="Refresh weather data"
          >
            <ArrowPathIcon className="h-6 w-6" />
          </button>
        </div>
      </div>

      {/* Main weather info */}
      <div className="relative flex items-center justify-between mb-8">
        <div className="flex items-center space-x-6">
          <div className="text-6xl animate-bounce-gentle">
            {weatherIcon}
          </div>
          
          <div>
            <div className="temperature-text">
              {Math.round(temp)}°C
            </div>
            <p className="text-lg text-gray-600 dark:text-gray-300 capitalize">
              {weatherInfo.description}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Feels like {Math.round(feels_like)}°C
            </p>
          </div>
        </div>
      </div>

      {/* Weather details grid */}
      <div className="relative grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="glass-effect p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
              <span className="text-blue-600 dark:text-blue-400 text-sm">💧</span>
            </div>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Humidity</p>
          <p className="text-lg font-semibold text-gray-900 dark:text-white">{humidity}%</p>
        </div>

        <div className="glass-effect p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
              <span className="text-green-600 dark:text-green-400 text-sm">🌪️</span>
            </div>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Wind</p>
          <p className="text-lg font-semibold text-gray-900 dark:text-white">
            {speed} m/s {deg && getWindDirection(deg)}
          </p>
        </div>

        <div className="glass-effect p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
              <span className="text-purple-600 dark:text-purple-400 text-sm">📊</span>
            </div>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Pressure</p>
          <p className="text-lg font-semibold text-gray-900 dark:text-white">{pressure} hPa</p>
        </div>

        <div className="glass-effect p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center">
              <EyeIcon className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Visibility</p>
          <p className="text-lg font-semibold text-gray-900 dark:text-white">
            {visibility ? `${(visibility / 1000).toFixed(1)} km` : 'N/A'}
          </p>
        </div>
      </div>

      {/* Sunrise/Sunset */}
      {sunrise && sunset && (
        <div className="relative mt-6 grid grid-cols-2 gap-4">
          <div className="glass-effect p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <SunIcon className="h-6 w-6 text-yellow-500" />
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Sunrise</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {formatSunTime(sunrise)}
            </p>
          </div>

          <div className="glass-effect p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">🌅</span>
              </div>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Sunset</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {formatSunTime(sunset)}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default CurrentWeather;
