import React from 'react';
import { ExclamationTriangleIcon, XMarkIcon } from '@heroicons/react/24/outline';

/**
 * Error message component with different styles and actions
 * @param {Object} props - Component props
 * @param {string|Error} props.error - Error message or Error object
 * @param {Function} props.onRetry - Retry function
 * @param {Function} props.onDismiss - Dismiss function
 * @param {string} props.type - Error type (error, warning, info)
 * @param {boolean} props.showIcon - Whether to show icon
 * @param {string} props.className - Additional CSS classes
 * @returns {JSX.Element} Error message component
 */
const ErrorMessage = ({ 
  error, 
  onRetry, 
  onDismiss, 
  type = 'error',
  showIcon = true,
  className = ''
}) => {
  if (!error) return null;

  const errorMessage = error instanceof Error ? error.message : error;

  const typeStyles = {
    error: {
      container: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800',
      text: 'text-red-800 dark:text-red-200',
      icon: 'text-red-500 dark:text-red-400',
      button: 'bg-red-100 hover:bg-red-200 dark:bg-red-800 dark:hover:bg-red-700 text-red-800 dark:text-red-200'
    },
    warning: {
      container: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800',
      text: 'text-yellow-800 dark:text-yellow-200',
      icon: 'text-yellow-500 dark:text-yellow-400',
      button: 'bg-yellow-100 hover:bg-yellow-200 dark:bg-yellow-800 dark:hover:bg-yellow-700 text-yellow-800 dark:text-yellow-200'
    },
    info: {
      container: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800',
      text: 'text-blue-800 dark:text-blue-200',
      icon: 'text-blue-500 dark:text-blue-400',
      button: 'bg-blue-100 hover:bg-blue-200 dark:bg-blue-800 dark:hover:bg-blue-700 text-blue-800 dark:text-blue-200'
    }
  };

  const styles = typeStyles[type];

  return (
    <div className={`
      rounded-lg border p-4 ${styles.container} ${className}
      animate-fade-in
    `}>
      <div className="flex items-start">
        {showIcon && (
          <div className="flex-shrink-0">
            <ExclamationTriangleIcon 
              className={`h-5 w-5 ${styles.icon}`}
              aria-hidden="true" 
            />
          </div>
        )}
        
        <div className={`${showIcon ? 'ml-3' : ''} flex-1`}>
          <h3 className={`text-sm font-medium ${styles.text}`}>
            {type === 'error' && 'Error'}
            {type === 'warning' && 'Warning'}
            {type === 'info' && 'Information'}
          </h3>
          
          <div className={`mt-1 text-sm ${styles.text}`}>
            <p>{errorMessage}</p>
          </div>
          
          {(onRetry || onDismiss) && (
            <div className="mt-4 flex space-x-2">
              {onRetry && (
                <button
                  type="button"
                  className={`
                    inline-flex items-center rounded-md px-3 py-2 text-sm font-medium
                    transition-colors duration-200 ${styles.button}
                  `}
                  onClick={onRetry}
                >
                  Try Again
                </button>
              )}
              
              {onDismiss && (
                <button
                  type="button"
                  className={`
                    inline-flex items-center rounded-md px-3 py-2 text-sm font-medium
                    transition-colors duration-200 ${styles.button}
                  `}
                  onClick={onDismiss}
                >
                  Dismiss
                </button>
              )}
            </div>
          )}
        </div>
        
        {onDismiss && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                type="button"
                className={`
                  inline-flex rounded-md p-1.5 transition-colors duration-200
                  hover:bg-black/5 dark:hover:bg-white/5 ${styles.icon}
                `}
                onClick={onDismiss}
              >
                <span className="sr-only">Dismiss</span>
                <XMarkIcon className="h-5 w-5" aria-hidden="true" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Weather-specific error message component
 * @param {Object} props - Component props
 * @param {string|Error} props.error - Error message
 * @param {Function} props.onRetry - Retry function
 * @param {string} props.city - City name that failed
 * @returns {JSX.Element} Weather error component
 */
export const WeatherError = ({ error, onRetry, city }) => {
  const getErrorTitle = () => {
    if (!error) return 'Unknown Error';
    
    const message = error instanceof Error ? error.message : error;
    
    if (message.includes('not found')) {
      return `City "${city}" not found`;
    }
    if (message.includes('denied')) {
      return 'Location access denied';
    }
    if (message.includes('timeout')) {
      return 'Request timed out';
    }
    if (message.includes('network') || message.includes('fetch')) {
      return 'Network error';
    }
    
    return 'Weather data unavailable';
  };

  const getErrorSuggestion = () => {
    if (!error) return 'Please try again later.';
    
    const message = error instanceof Error ? error.message : error;
    
    if (message.includes('not found')) {
      return 'Please check the spelling and try again.';
    }
    if (message.includes('denied')) {
      return 'Please enable location access in your browser settings.';
    }
    if (message.includes('timeout')) {
      return 'Please check your internet connection and try again.';
    }
    if (message.includes('network') || message.includes('fetch')) {
      return 'Please check your internet connection.';
    }
    
    return 'Please try again in a few moments.';
  };

  return (
    <div className="weather-card p-8 text-center">
      <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20 mb-4">
        <ExclamationTriangleIcon 
          className="h-8 w-8 text-red-600 dark:text-red-400" 
          aria-hidden="true" 
        />
      </div>
      
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
        {getErrorTitle()}
      </h3>
      
      <p className="text-gray-600 dark:text-gray-400 mb-6">
        {getErrorSuggestion()}
      </p>
      
      {onRetry && (
        <button
          onClick={onRetry}
          className="btn-primary"
        >
          Try Again
        </button>
      )}
    </div>
  );
};

/**
 * Network status error component
 * @param {Object} props - Component props
 * @param {boolean} props.isOnline - Network status
 * @returns {JSX.Element|null} Network error component
 */
export const NetworkError = ({ isOnline }) => {
  if (isOnline) return null;

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
      <div className="bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
        <ExclamationTriangleIcon className="h-5 w-5" />
        <span className="text-sm font-medium">No internet connection</span>
      </div>
    </div>
  );
};

export default ErrorMessage;
